<%@ Page Language="C#" Title="基表管理中心" Inherits="BasePage" %>

<script runat="server">
    protected void Page_Load(object sender, EventArgs e)
    {

        rptitle.DataSource = _sys.GetNewDictionaryItems("4d1261bb3e971ca107b2");//数据采集表，数据字典
        rptitle.DataBind();

        //var session = dal.GetData<SysSession>();
        //rptyear.DataSource = session.GroupBy(p => p.Annual).Select(p =>
        //{
        //    var startD = p.Min(i => DateTime.Parse(i.BeginDate));
        //    var endD = p.Max(x => DateTime.Parse(x.EndDate));
        //    return new { ID = p.Key, Text = string.Format("{0}~{1}学年({2}~{3})", startD.ToString("yyyy"), endD.ToString("yyyy"), startD.ToString("yyyyMMdd"), endD.ToString("yyyyMMdd")) };
        //}).ToList<object>();
        //rptyear.DataBind();

        var sessions = dal.GetData<LabDataCollectMain>();
        Semester.DataSource = sessions.GroupBy(p => p.sessionId).Select(p =>
        {
            //var startD = p.Min(i => DateTime.Parse(i.BeginDate));
            //var endD = p.Max(x => DateTime.Parse(x.EndDate));
            return new { ID = p.Key, Text = p.Key };
        }).ToList<object>();
        Semester.DataBind();
        //sel2.DataSource = dal.GetData<SysDepaterment>(w.Equals("TypeID", "da8fe42c4bfacbc5e785")).Select(p => new { name = p.DepatermentName, value = p.DeptID }).ToList<object>();
    }
</script>

<div class="layui-form">
    <div class="layui-fluid">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body" id="title">
                        <asp:repeater runat="server" id="rptitle">
                            <itemtemplate>
                                <div data-id="<%# Eval("ExtID") %>" data-select="0" data-tid="<%# Eval("ExtID") %>"><%# Eval("TargetName") %></div>
                            </itemtemplate>
                        </asp:repeater>
                    </div>
                    <div class="clear"></div>
                </div>
            </div>
        </div>
        <div class="tbarea">
            <div class="toolbar">
                <div class="tgp">
                   <%-- <uc:Select runat="server" ID="rptyear" ControlID="rptyear" LabelText="选择上报学年" Width="250" />--%>
                    <uc:TextBox ControlID="key" Buttons="[{icon:'Niconfont layui-icon-extend-sousuo1',event:'search'}]" LabelText="填报人" runat="server" />
                    <uc:SelectMulti ID="Semester" runat="server" ControlID="Semester" LabelText="选择上报学期（多选）"></uc:SelectMulti>
                    <button lay-event="search" class="btnsearch" style="padding: 8px 25px;
                                                    border: 1px solid #d9ddf0;
                                                    font-size: 12px;
                                                    border-radius: 5px;
                                                    height: 47px;
                                                    background-color: green;
                                                    color: white;
                                                    cursor: pointer;
                                                    margin-left: 10px;">查询</button>
                </div>
                <div class="gjl">
                    <uc:tabTool runat="server" />
                </div>
                <div class="clear"></div>
            </div>
            <div class="gaojisoussuo layui-form">
                <div class="tgp">
                </div>

                <div class="clear"></div>
            </div>
        </div>





        <div class="tabdv">
            <table class="layui-hide" id="QEList" lay-filter="QEList" lay-skin="line"></table>
        </div>
    </div>
</div>
<uc:tabTool runat="server" TypeID="1" BarName="colBtns" />
<script>

    var cols = [];     // 当前表结构
    var colTexts = [];

    $("#title>div").click(function (d) {
        $("#title>div").css("background", "");
        $("#title>div").css("color", "");
        $("#title>div").each((i, v) => $(v).attr('data-select', '0'))
        $(d.target).css("background", "#F0F1F6");
        $(d.target).css("color", "#0D33F1");
        $(d.target).attr("data-select", "1");

        //获取动态列
        var sid = $(d.target).attr("data-id");
        cols = [];        
        colTexts = [];
        cols.push({ type: 'checkbox' });
        cols.push({ type: "numbers", width: 70, title: '序号' });
        cols.push({
            field: 'session', width: 130, title: '所属学期', templet: (d) => {
                //return `${d.SessionID.substr(0, 4)} ~ ${d.SessionID.substr(4)}学年`
                return d.SessionID
            }
        });

        tools.Do({
            url: "Sys/GetNewDictionaryItems",
            noAsync: true,
            data: { ExtID: sid },
            callback: function (res) {
                if (res && res.err != "err" && Array.isArray(res)) {
                    var str = "";
                    $.each(res, function (index, element) {
                        if (element && element.TargetName) {
                            colTexts.push(element.TargetName);
                            cols.push({ field: element.TargetName, width: 130, title: element.TargetName });
                        }
                    })
                } else {
                    tools.myAlert(res && res.msg ? res.msg : '获取字典项失败');
                    console.error('获取字典项失败:', res);
                }
            },
            error: function(err) {
                tools.myAlert('网络请求失败，请稍后重试');
                console.error('请求字典项失败:', err);
            }
        })

        cols.push({
            field: 'state', width: 90, title: '状态', templet: (d) => {
                return ["未提交", "已提交", "已驳回"][d.State];
            }
        });
        cols.push({ field: 'Name', width: 90, title: '创建人' });
        cols.push({
            field: 'crdate', width: 110, title: '创建日期', templet: (d) => {
                return tools.FmtDate(d.Crdate)
            }
        });
        cols.push({
            templet: '#colBtns', width: 100, align: 'center', title: '操作', fixed: 'right'
        });
        
        loadData(1);
        loadSessionID();
    });

    $("#title>div").first().click();

    function loadSessionID() {       
        let item = $('#title>div[data-select=1]');
        let sid = item.data('id')
        if (!sid) {
            console.warn('未选择数据采集表');
            return;
        }
        tools.Do({
            url: 'Lab/GetDataSessionID',
            data: {
                collectManageId: sid,                          
                createrId: $("#key").val(),
            },
            callback(res) {
                if (res && res.msg && res.msg.data) {
                    const { data, list } = res.msg;                         
                    formSelects.render('Semester');               
                    formSelects.data('Semester', 'local', {
                        arr: data
                    });
                } else {
                    console.error('获取学期数据失败:', res);
                }
            },
            error: function(err) {
                console.error('请求学期数据失败:', err);
            }
        })
    }
    function calculateTotals(tableData) {
        let totals = {};
        
        // 检查数据是否为空
        if (!tableData || !Array.isArray(tableData) || tableData.length === 0) {
            colTexts.forEach(field => {
                totals[field] = 0;
            });
            return totals;
        }
        
        // 初始化合计对象（以动态列名为key）
        colTexts.forEach(field => {
            totals[field] = 0;
        });
        
        // 遍历表格数据，累加数值型字段
        tableData.forEach(row => {
            if (row) {
                colTexts.forEach(field => {
                    let value = row[field];
                    // 只对数值型数据求和（排除非数字值）
                    if (!isNaN(parseFloat(value)) && value !== null && value !== undefined && value !== '') {
                        totals[field] += parseFloat(value) || 0;
                    }
                });
            }
        });
        
        return totals;
    }
    function addTotalRow(totals) {
        // 先移除已存在的合计行
        let existingTotalRow = document.querySelector('.layui-table-total-row');
        if (existingTotalRow) {
            existingTotalRow.remove();
        }
        
        // 获取表格的 tbody 元素（LayUI 表格的 tbody 通常有 class "layui-table-body layui-table-main"）
        let tableBody = document.querySelector('#QEList').nextElementSibling.querySelector('.layui-table-main tbody');
        if (!tableBody) return;

        // 1. 创建合计行 tr 元素
        let totalRow = document.createElement('tr');
        totalRow.className = 'layui-table-total-row'; // 自定义样式类
        totalRow.style.backgroundColor = '#f5f7fa'; // 合计行背景色
        totalRow.style.fontWeight = 'bold'; // 加粗文字

        // 2. 生成合计行的 td 元素（与表格列对应）
        // 第1列：复选框列（显示"合计"）
        let checkboxTd = document.createElement('td');
        checkboxTd.style.textAlign = 'center';  // 水平居中
        checkboxTd.style.verticalAlign = 'middle';  // 垂直居中   
        checkboxTd.style.padding = '18px 10px';
        checkboxTd.colSpan = 1;
        checkboxTd.textContent = '合计'; // 固定显示"合计"
        totalRow.appendChild(checkboxTd);

        // 第2列：序号列（显示"-"）
        let indexTd = document.createElement('td');
        indexTd.style.textAlign = 'center';  // 水平居中
        indexTd.style.verticalAlign = 'middle';  // 垂直居中
        indexTd.style.padding = '18px 10px';
        indexTd.colSpan = 1;
        indexTd.textContent = '-';
        totalRow.appendChild(indexTd);

        // 第3列：所属学期（非合计列，空或显示"-"）
        let sessionTd = document.createElement('td');
        sessionTd.style.textAlign = 'center';  // 水平居中
        sessionTd.style.verticalAlign = 'middle';  // 垂直居中  
        sessionTd.style.padding = "18px 10px";
        sessionTd.colSpan = 1;
        sessionTd.textContent = '-';
        totalRow.appendChild(sessionTd);

        // 动态列：填充计算好的合计值
        colTexts.forEach(field => {
            let td = document.createElement('td');
            td.style.textAlign = 'center';  // 水平居中
            td.style.verticalAlign = 'middle';  // 垂直居中
            td.style.padding = '18px 10px';
            // 如果有合计值且大于0，显示数值，否则显示"-"
            if (totals[field] && totals[field] > 0) {
                td.textContent = totals[field].toLocaleString(); // 使用千分位分隔符
            } else {
                td.textContent = '-';
            }
            totalRow.appendChild(td);
        });

        // 状态列、创建人、创建日期列（非合计列，空或显示"-"）
        let nonTotalFields = ['state', 'Name', 'crdate', '操作'];
        nonTotalFields.forEach(() => {
            let td = document.createElement('td');
            td.style.textAlign = 'center';  // 水平居中
            td.style.verticalAlign = 'middle';  // 垂直居中
            td.style.padding = "18px 10px";
            td.textContent = '-';
            totalRow.appendChild(td);
        });

        // 3. 将合计行插入到表格 tbody 的最后
        tableBody.appendChild(totalRow);        
    }


    function loadData(cur) {
        let item = $('#title>div[data-select=1]');
        let sid = item.data('id')
        
        if (!sid) {
            tools.myAlert('请先选择数据采集表');
            return;
        }

        var tab1 = tools.LoadDataTable({
            table: table,
            elm: 'QEList',
            multiSelect: true,
            url: 'Lab/GetLabDataCollectData',
            totalRow: false,
            jump: function (p) {
                loadData(p);
            },
            page: true,
            data: {
                page: cur,
                collectManageId: sid,
                state: "",
                //sessionId: $("#rptyear").val(),
                sessionId: JSON.stringify(Semester.getValue().map(p => { return { id: p.value, name: p.name } })),
                createrId: $("#key").val(),
                cols: colTexts.join(','),
                ids:[]
            },
            rowClick: (res, event) => { },
            cols: cols
            ,
            //done: function () {
            //},
            // 表格渲染完成后的回调（关键）
            done: function (res, curr, count) {               
                try {
                    // res 是接口返回的完整数据（包含表格数据）
                    // 1. 计算合计值
                    let totals = calculateTotals(res && res.data ? res.data : []);
                    // 2. 动态创建并插入合计行
                    addTotalRow(totals);
                } catch (error) {
                    console.error('渲染合计行失败:', error);
                }
            },
            toolBarEvent: [
                function adv() {
                    tools.AdvToggle($(this));
                    table.resize('QEList');

                },
                function search() {
                    loadData(1);

                },
                function setting() {
                    tools.ShowLayer({
                        url: "Lab/baseRpt/DataCollectManageSetting",
                        title: "新增数据采集设置",
                        data: {
                        },
                        btns: [

                            {
                                text: '关闭'
                            }
                        ],
                        size: { w: 1200, h: 760 }
                    });
                },
                function export1() {
                    tools.Do({
                        url: 'Lab/GetLabDataCollectData',
                        data: {
                            page: cur ? cur : 1,
                            rows: 1,
                            collectManageId: sid,
                            state: "",
                            sessionId: JSON.stringify(Semester.getValue().map(p => { return { id: p.value, name: p.name } })),
                            createrId: $("#key").val(),
                            cols: colTexts.join(','),
                            ids: table.checkStatus("QEList").data.map(p => p.ID),
                            isexport: true
                        },
                        callback(res) {
                            tools.DownLoadFile({ url: res.msg, name: '数据采集管理.xls' });
                        }
                    })
                },
                function reject() {

                    if (table.checkStatus("QEList").data.length == 0) return tools.myAlert("请选择要驳回的数据");
                    tools.Confirm({
                        msg: '是否确定驳回数据',
                        btns: [
                            {
                                text: '驳回',
                                method: function () {
                                    tools.Do({
                                        url: "Lab/SubmitLabDataCollect"
                                        , data: {
                                            ids: table.checkStatus("QEList").data.map(p => p.ID),
                                            state: "2"
                                        }
                                        , callback: function (res) {
                                            tools.CloseLayer();
                                            loadData(1);
                                        }
                                    });

                                }
                            }
                        ]
                    });
                }
            ],
            toolEvent: [
                function reject(obj) {
                    tools.Confirm({
                        msg: '是否确定驳回数据',
                        btns: [
                            {
                                text: '驳回',
                                method: function () {
                                    tools.Do({
                                        url: "Lab/SubmitLabDataCollect"
                                        , data: {
                                            ids: [obj.data.ID],
                                            state: "2"
                                        }
                                        , callback: function (res) {
                                            tools.CloseLayer();
                                            loadData(1);
                                        }
                                    });

                                }
                            }
                        ]
                    });
                },


            ]
        });
    }
    form.render();

</script>

<style>
    .custom-select-box {
    margin-top: -4.2px;
    width: 202px !important;
}
    .layui-card {
        color: #A2A2A2;
        margin: 6px !important;
        box-shadow: 0 1px 3px 0 rgb(0 0 0 / 30%);
    }

    .layui-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

        .layui-card-header > span {
            padding: 0px 7px;
            border-radius: 2px;
            font-size: 10px;
        }

    .layui-card .layui-card-body {
        display: flex;
        align-items: center;
        padding: 10px;
    }

        .layui-card .layui-card-body span {
            font-size: 32px;
            color: #66686B;
            padding: 10px;
        }

        .layui-card .layui-card-body div {
            margin: 2px 5px;
            padding: 5px 20px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
        }

    .body-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px;
    }

        .body-footer > div > img {
            width: 24px;
            height: 24px;
        }

    .chartchild {
        height: 360px;
    }

    #title {
        flex-wrap: wrap;
    }

        #title > div {
            white-space: nowrap;
        }

    .ss a.selected {
        background-color: #1E9FFF;
        padding: 5px 10px;
        color: #fff;
        font-weight: bold;
        border-radius: 5px;
    }
    

/* 合计行整体样式 */
.layui-table-total-row {
    background-color: #f8f9fa;        /* 背景颜色 */
    color: #333;                     /* 文字颜色 */
    font-weight: bold;               /* 文字加粗 */
    border-top: 2px solid #dee2e6;   /* 上边框 */
    border-bottom: 1px solid #dee2e6; /* 下边框 */
    position: sticky;
    bottom: -2px;
    z-index: 10;
    margin-top: 10px;
}

/* 合计行单元格样式 */
.layui-table-total-row td {
    padding: 18px 10px;              /* 内边距 */
    text-align: center;              /* 文本居中 */
    font-size: 14px;                 /* 字体大小 */
    height: 39.5px;
    position: relative;
    box-sizing: border-box;
    color: #333;
    vertical-align: middle;
}

/* 特定列样式（例如第5列之后的数值列） */
.layui-table-total-row td:nth-child(n+5) {
   /* font-style: italic;    */          /* 斜体 */
}/* 固定在表格底部的合计行容器 */
.total-row-container {
    position: sticky;
    bottom: 0;
    z-index: 10;
    background-color: white;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.05); /* 顶部阴影 */
}
/* 悬停时样式变化 */
.layui-table-total-row:hover {
    background-color: #f1f3f5;
    transition: background-color 0.2s; /* 平滑过渡 */
}
/* "合计"文本样式 */
.layui-table-total-row td:nth-child(2) {
    /*background-color: #e9ecef;*/
    border-radius: 4px 0 0 4px;      /* 圆角 */
}
</style>
